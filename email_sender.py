import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
import logging

# Konfigurace SMTP serveru
SMTP_SERVER = "smtp.websupport.cz"
SMTP_PORT = 587  # Port pro TLS
FROM_NAME = "Distribox"
SMTP_USER = "<EMAIL>"
SMTP_PASS = "uTCf,%(!evt1Cpa/5VM("

def send_email(to_email, subject, message, html_message=None):
    """
    Odešle email pomocí SMTP serveru.
    
    Args:
        to_email (str): Email adresa příjemce
        subject (str): Předmět emailu
        message (str): Textová zpráva
        html_message (str, optional): HTML verze zprávy
    
    Returns:
        bool: True pokud byl email úspěšně odeslán, False jinak
    """
    try:
        # Vytvoření zprávy
        msg = MIMEMultipart('alternative')
        msg['From'] = f"{FROM_NAME} <{SMTP_USER}>"
        msg['To'] = to_email
        msg['Subject'] = Header(subject, 'utf-8')
        
        # Přidání textové části
        text_part = MIMEText(message, 'plain', 'utf-8')
        msg.attach(text_part)
        
        # Přidání HTML části pokud je poskytnutá
        if html_message:
            html_part = MIMEText(html_message, 'html', 'utf-8')
            msg.attach(html_part)
        
        # Připojení k SMTP serveru
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
        server.starttls()  # Zapnutí TLS šifrování
        server.login(SMTP_USER, SMTP_PASS)
        
        # Odeslání emailu
        text = msg.as_string()
        server.sendmail(SMTP_USER, to_email, text)
        server.quit()
        
        print(f"Email úspěšně odeslán na adresu: {to_email}")
        return True
        
    except smtplib.SMTPAuthenticationError:
        print("Chyba: Neplatné přihlašovací údaje SMTP")
        return False
    except smtplib.SMTPRecipientsRefused:
        print(f"Chyba: Neplatná email adresa příjemce: {to_email}")
        return False
    except smtplib.SMTPServerDisconnected:
        print("Chyba: Spojení se SMTP serverem bylo přerušeno")
        return False
    except Exception as e:
        print(f"Chyba při odesílání emailu: {str(e)}")
        return False

def send_simple_email(to_email, subject, message):
    """
    Zjednodušená verze funkce pro odesílání pouze textových emailů.
    
    Args:
        to_email (str): Email adresa příjemce
        subject (str): Předmět emailu
        message (str): Textová zpráva
    
    Returns:
        bool: True pokud byl email úspěšně odeslán, False jinak
    """
    return send_email(to_email, subject, message)

# Příklad použití
if __name__ == "__main__":
    # Test funkce
    test_email = "<EMAIL>"  # Změňte na skutečnou email adresu pro test
    test_subject = "Test email"
    test_message = "Toto je testovací zpráva z Python skriptu."
    
    # Odeslání testovacího emailu
    success = send_simple_email(test_email, test_subject, test_message)
    
    if success:
        print("Test byl úspěšný!")
    else:
        print("Test selhal!")
